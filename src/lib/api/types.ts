// 百度股市通API相关类型定义

// 百度API原始响应结构
export interface BaiduApiResponse {
  QueryID: string;
  Result: BaiduApiResult[];
  ResultCode: string;
  ResultNum: string;
}

export interface BaiduApiResult {
  ClickNeed: string;
  DisplayData: {
    StdStg: string;
    StdStl: string;
    resultData: {
      extData: {
        OriginQuery: string;
        resourceid: string;
        tplt: string;
      };
      tplData: {
        ResultURL: string;
        card_order: string;
        data_source: string;
        digits: string;
        disp_data_url_ex: {
          aesplitid: string;
        };
        lyAxis: any[];
        maxPoints: string;
        sec: number;
        series: BaiduApiSeries[];
        showDate: string;
        showTag: string;
        text: string;
        xAxis: string[];
      };
    };
    strategy: {
      ctplOrPhp: string;
      hilightWord: string;
      precharge: string;
      tempName: string;
    };
  };
  OriginSrcID: string;
  RecoverCacheTime: string;
  ResultURL: string;
  Sort: string;
  SrcID: string;
  SubResNum: string;
  SubResult: any[];
  Weight: string;
}

export interface BaiduApiSeries {
  label: string[];
  name: string;
  value: string; // 格式: "日期,净值,涨幅,累计净值;..."
}

// 解析后的基金数据点
export interface ParsedFundDataPoint {
  date: string;
  netAssetValue: number;
  dailyChange: string;
  accumulatedValue: number;
}

// API请求参数
export interface FundApiParams {
  fundCode: string;
  startDate?: string;
  endDate?: string;
  dataType?: 'nvl' | 'price'; // nvl=净值, price=价格
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 基金基本信息（从其他API获取）
export interface FundBasicInfo {
  code: string;
  name: string;
  type: string;
  manager: string;
  establishDate: string;
  scale?: string;
  description?: string;
}

// 缓存项结构
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// API响应状态
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

// 数据获取选项
export interface DataFetchOptions {
  useCache?: boolean;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}
