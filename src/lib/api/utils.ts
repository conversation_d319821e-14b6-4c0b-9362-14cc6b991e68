// API工具函数

import type { 
  BaiduApiResponse, 
  ParsedFundDataPoint, 
  ApiError, 
  CacheItem,
  DataFetchOptions 
} from './types';

// 内存缓存
const cache = new Map<string, CacheItem<any>>();

/**
 * 解析百度API返回的基金净值数据
 */
export function parseFundData(response: BaiduApiResponse): ParsedFundDataPoint[] {
  try {
    if (!response.Result || response.Result.length === 0) {
      throw new Error('API返回数据为空');
    }

    const result = response.Result[0];
    const series = result.DisplayData?.resultData?.tplData?.series;
    
    if (!series || series.length === 0) {
      throw new Error('未找到基金数据');
    }

    // 获取净值曲线数据
    const netValueSeries = series.find(s => s.name === '净值曲线');
    if (!netValueSeries) {
      throw new Error('未找到净值曲线数据');
    }

    // 解析数据字符串
    const dataPoints: ParsedFundDataPoint[] = [];
    const dataEntries = netValueSeries.value.split(';');

    for (const entry of dataEntries) {
      if (!entry.trim()) continue;

      const parts = entry.split(',');
      if (parts.length >= 4) {
        const [dateStr, netValue, dailyChange, accValue] = parts;
        
        // 转换日期格式 (2025-05-06 -> 2025-05-06)
        const date = formatDate(dateStr);
        
        dataPoints.push({
          date,
          netAssetValue: parseFloat(netValue),
          dailyChange: dailyChange,
          accumulatedValue: parseFloat(accValue),
        });
      }
    }

    return dataPoints.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } catch (error) {
    console.error('解析基金数据失败:', error);
    throw new Error(`数据解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 格式化日期字符串
 */
export function formatDate(dateStr: string): string {
  // 如果已经是 YYYY-MM-DD 格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }

  // 如果是 MM-DD 格式，添加当前年份
  if (/^\d{2}-\d{2}$/.test(dateStr)) {
    const currentYear = new Date().getFullYear();
    return `${currentYear}-${dateStr}`;
  }

  // 其他格式尝试解析
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) {
    throw new Error(`无效的日期格式: ${dateStr}`);
  }

  return date.toISOString().split('T')[0];
}

/**
 * 构建百度API请求URL
 */
export function buildApiUrl(fundCode: string, options: { dataType?: string } = {}): string {
  const baseUrl = 'https://gushitong.baidu.com/opendata';
  const params = new URLSearchParams({
    resource_id: '5824',
    query: fundCode,
    new_need_di: '1',
    m: '1',
    t: options.dataType || 'nvl',
    finClientType: 'pc'
  });

  return `${baseUrl}?${params.toString()}`;
}

/**
 * 验证基金代码格式
 */
export function validateFundCode(code: string): boolean {
  // 基金代码通常是6位数字
  return /^\d{6}$/.test(code);
}

/**
 * 创建API错误对象
 */
export function createApiError(code: string, message: string, details?: any): ApiError {
  return {
    code,
    message,
    details
  };
}

/**
 * 缓存管理
 */
export class CacheManager {
  private static instance: CacheManager;
  private cache = new Map<string, CacheItem<any>>();

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

/**
 * 重试机制
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: { maxRetries?: number; delay?: number } = {}
): Promise<T> {
  const { maxRetries = 3, delay = 1000 } = options;
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (i === maxRetries) {
        throw lastError;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError!;
}

/**
 * 请求超时处理
 */
export function withTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout);
    })
  ]);
}

/**
 * 生成缓存键
 */
export function generateCacheKey(fundCode: string, startDate?: string, endDate?: string): string {
  const parts = [fundCode];
  if (startDate) parts.push(startDate);
  if (endDate) parts.push(endDate);
  return parts.join('_');
}

/**
 * 日期范围验证
 */
export function validateDateRange(startDate: string, endDate: string): boolean {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return false;
  }

  return start <= end;
}

/**
 * 格式化错误消息
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return '未知错误';
}
