import type { Fund, Index, FundData, IndexData } from "@/types/fund";
import { APP_CONFIG } from "@/lib/config";
import { fetchFundData, getFundBasicInfo } from "@/lib/api/fundApi";

// 模拟基金数据
export const mockFunds: Fund[] = [
  {
    id: "fund_001",
    name: "易方达沪深300ETF联接A",
    code: "110020",
    type: "index",
    indexId: "index_hs300",
    description: "跟踪沪深300指数的指数基金",
  },
  {
    id: "fund_002",
    name: "华夏中证500ETF联接A",
    code: "000478",
    type: "index",
    indexId: "index_zz500",
    description: "跟踪中证500指数的指数基金",
  },
  {
    id: "fund_003",
    name: "南方创业板ETF联接A",
    code: "002656",
    type: "index",
    indexId: "index_cyb",
    description: "跟踪创业板指数的指数基金",
  },
  {
    id: "fund_004",
    name: "兴全合润混合",
    code: "163406",
    type: "hybrid",
    description: "灵活配置混合型基金",
  },
  {
    id: "fund_005",
    name: "易方达蓝筹精选混合",
    code: "005827",
    type: "hybrid",
    description: "主要投资大盘蓝筹股的混合基金",
  },
];

// 模拟指数数据
export const mockIndices: Index[] = [
  {
    id: "index_hs300",
    name: "沪深300",
    code: "000300",
    description: "沪深两市规模大、流动性好的300只股票",
  },
  {
    id: "index_zz500",
    name: "中证500",
    code: "000905",
    description: "中等市值股票的代表性指数",
  },
  {
    id: "index_cyb",
    name: "创业板指",
    code: "399006",
    description: "创业板市场的核心指数",
  },
];

// 生成模拟基金历史数据
export function generateMockFundData(
  fundId: string,
  startDate: string,
  endDate: string
): FundData[] {
  const data: FundData[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const currentDate = new Date(start);
  let baseValue = 1 + Math.random() * 2; // 基础净值 1.0-3.0

  // 根据基金类型设置不同的波动性
  const fund = mockFunds.find((f) => f.id === fundId);
  let volatility = 0.02; // 默认波动率
  let trend = 0.0001; // 默认趋势

  if (fund) {
    switch (fund.type) {
      case "index":
        volatility = 0.025;
        trend = 0.0002;
        break;
      case "hybrid":
        volatility = 0.02;
        trend = 0.0003;
        break;
      case "stock":
        volatility = 0.03;
        trend = 0.0001;
        break;
      case "bond":
        volatility = 0.005;
        trend = 0.0001;
        break;
    }
  }

  while (currentDate <= end) {
    // 跳过周末
    if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
      // 生成随机价格变动
      const randomChange = (Math.random() - 0.5) * volatility;
      const trendChange = trend;

      baseValue = baseValue * (1 + randomChange + trendChange);

      // 添加一些市场事件的影响
      if (Math.random() < 0.01) {
        // 1%概率的大幅波动
        const eventImpact = (Math.random() - 0.5) * 0.1;
        baseValue = baseValue * (1 + eventImpact);
      }

      // 确保净值不会变成负数
      baseValue = Math.max(baseValue, 0.1);

      data.push({
        date: currentDate.toISOString().split("T")[0],
        netAssetValue: Number(baseValue.toFixed(4)),
        accumulatedValue: Number((baseValue * 1.2).toFixed(4)), // 累计净值通常更高
        volume: Math.floor(Math.random() * 1000000) + 100000,
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return data;
}

// 生成模拟指数历史数据
export function generateMockIndexData(
  indexId: string,
  startDate: string,
  endDate: string
): IndexData[] {
  const data: IndexData[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const currentDate = new Date(start);

  // 根据指数设置基础值
  let baseValue = 3000;
  let basePE = 15;
  let basePB = 1.5;

  const index = mockIndices.find((i) => i.id === indexId);
  if (index) {
    switch (index.code) {
      case "000300": // 沪深300
        baseValue = 4000;
        basePE = 12;
        basePB = 1.3;
        break;
      case "000905": // 中证500
        baseValue = 6000;
        basePE = 20;
        basePB = 2;
        break;
      case "399006": // 创业板指
        baseValue = 2500;
        basePE = 35;
        basePB = 3;
        break;
    }
  }

  while (currentDate <= end) {
    // 跳过周末
    if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
      // 生成随机变动
      const randomChange = (Math.random() - 0.5) * 0.03;
      const trendChange = 0.0002;

      baseValue = baseValue * (1 + randomChange + trendChange);
      basePE = basePE * (1 + (Math.random() - 0.5) * 0.02);
      basePB = basePB * (1 + (Math.random() - 0.5) * 0.02);

      // 确保指标在合理范围内
      baseValue = Math.max(baseValue, 1000);
      basePE = Math.max(Math.min(basePE, 100), 5);
      basePB = Math.max(Math.min(basePB, 10), 0.5);

      data.push({
        date: currentDate.toISOString().split("T")[0],
        value: Number(baseValue.toFixed(2)),
        pe: Number(basePE.toFixed(2)),
        pb: Number(basePB.toFixed(2)),
        volume: Math.floor(Math.random() * 10000000) + 1000000,
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return data;
}

// 获取基金数据的API（支持真实数据和模拟数据）
export async function getFundData(
  fundId: string,
  startDate: string,
  endDate: string
): Promise<FundData[]> {
  // 检查是否使用模拟数据
  if (APP_CONFIG.dataSource.useMockData) {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    return generateMockFundData(fundId, startDate, endDate);
  }

  // 使用真实数据
  try {
    // 如果fundId是基金代码格式，直接使用
    let fundCode = fundId;

    // 如果是内部ID，需要转换为基金代码
    if (!fundId.match(/^\d{6}$/)) {
      const fund = mockFunds.find(f => f.id === fundId);
      if (fund) {
        fundCode = fund.code;
      } else {
        throw new Error(`未找到基金ID: ${fundId}`);
      }
    }

    const realData = await fetchFundData(fundCode, startDate, endDate);

    // 如果真实数据为空，回退到模拟数据
    if (realData.length === 0) {
      console.warn(`基金${fundCode}真实数据为空，使用模拟数据`);
      return generateMockFundData(fundId, startDate, endDate);
    }

    return realData;
  } catch (error) {
    console.error(`获取基金${fundId}真实数据失败，使用模拟数据:`, error);
    return generateMockFundData(fundId, startDate, endDate);
  }
}

// 获取指数数据的API模拟
export async function getIndexData(
  indexId: string,
  startDate: string,
  endDate: string
): Promise<IndexData[]> {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  return generateMockIndexData(indexId, startDate, endDate);
}

// 获取所有基金列表
export async function getFunds(): Promise<Fund[]> {
  await new Promise((resolve) => setTimeout(resolve, 200));
  return mockFunds;
}

// 获取所有指数列表
export async function getIndices(): Promise<Index[]> {
  await new Promise((resolve) => setTimeout(resolve, 200));
  return mockIndices;
}

// 根据基金ID获取基金信息
export function getFundById(fundId: string): Fund | undefined {
  return mockFunds.find((fund) => fund.id === fundId);
}

// 根据指数ID获取指数信息
export function getIndexById(indexId: string): Index | undefined {
  return mockIndices.find((index) => index.id === indexId);
}

// 搜索基金（支持代码和名称）
export async function searchFunds(query: string): Promise<Fund[]> {
  if (!query.trim()) {
    return mockFunds;
  }

  const queryLower = query.toLowerCase();

  // 先在本地基金列表中搜索
  const localResults = mockFunds.filter(fund =>
    fund.code.includes(query) ||
    fund.name.toLowerCase().includes(queryLower)
  );

  // 如果不使用模拟数据且查询看起来像基金代码，尝试验证真实基金
  if (!APP_CONFIG.dataSource.useMockData && query.match(/^\d{6}$/)) {
    try {
      const fundInfo = await getFundBasicInfo(query);
      if (fundInfo && !localResults.some(f => f.code === query)) {
        // 添加新发现的基金到结果中
        localResults.push({
          id: `fund_${query}`,
          name: fundInfo.name,
          code: fundInfo.code,
          type: 'hybrid', // 默认类型，实际应从API获取
          description: `通过代码${query}搜索到的基金`
        });
      }
    } catch (error) {
      console.warn(`验证基金代码${query}失败:`, error);
    }
  }

  return localResults;
}

// 验证基金代码是否有效
export async function validateFund(fundCode: string): Promise<boolean> {
  // 先检查本地列表
  const localFund = mockFunds.find(f => f.code === fundCode);
  if (localFund) {
    return true;
  }

  // 如果不使用模拟数据，检查真实API
  if (!APP_CONFIG.dataSource.useMockData) {
    try {
      const { validateFund } = await import('@/lib/api/fundApi');
      return await validateFund(fundCode);
    } catch (error) {
      console.error(`验证基金代码${fundCode}失败:`, error);
      return false;
    }
  }

  return false;
}

// 添加新基金到列表（用于动态发现的基金）
export function addFundToList(fund: Fund): void {
  const existingIndex = mockFunds.findIndex(f => f.code === fund.code);
  if (existingIndex >= 0) {
    // 更新现有基金信息
    mockFunds[existingIndex] = { ...mockFunds[existingIndex], ...fund };
  } else {
    // 添加新基金
    mockFunds.push(fund);
  }
}
