'use client';

import { useState } from 'react';
import { BacktestEngine } from '@/lib/backtest';
import { getFundData, getIndexData, mockFunds } from '@/lib/mockData';
import type { GridTradingParams, BacktestResult } from '@/types/fund';

export default function GridTradingDemo() {
  const [result, setResult] = useState<BacktestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 默认网格交易参数
  const defaultParams: GridTradingParams = {
    startDate: '2023-01-01',
    endDate: '2024-01-01',
    initialAmount: 50000,
    gridCount: 10,
    priceRange: {
      min: 1.0,
      max: 3.0
    },
    investmentPerGrid: 2000,
    rebalanceFrequency: 'weekly',
    stopLoss: 20,
    takeProfit: 50
  };

  const [params, setParams] = useState<GridTradingParams>(defaultParams);

  const runGridTradingTest = async () => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      // 使用第一个基金进行测试
      const fund = mockFunds[0];
      
      // 获取基金数据
      const fundData = await getFundData(
        fund.id,
        params.startDate,
        params.endDate
      );

      // 获取指数数据（如果基金关联了指数）
      let indexData;
      if (fund.indexId) {
        indexData = await getIndexData(
          fund.indexId,
          params.startDate,
          params.endDate
        );
      }

      // 创建回测引擎并执行回测
      const engine = new BacktestEngine(fundData, indexData);
      const backtestResult = await engine.runBacktest(fund, params);

      setResult(backtestResult);
    } catch (err) {
      console.error('网格交易回测失败:', err);
      setError(err instanceof Error ? err.message : '回测失败');
    } finally {
      setLoading(false);
    }
  };

  const updateParam = (key: keyof GridTradingParams, value: any) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updatePriceRange = (type: 'min' | 'max', value: number) => {
    setParams(prev => ({
      ...prev,
      priceRange: {
        ...prev.priceRange,
        [type]: value
      }
    }));
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">网格交易策略演示</h2>
        
        {/* 参数设置 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">基础参数</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                开始日期
              </label>
              <input
                type="date"
                value={params.startDate}
                onChange={(e) => updateParam('startDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                结束日期
              </label>
              <input
                type="date"
                value={params.endDate}
                onChange={(e) => updateParam('endDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                初始资金 (元)
              </label>
              <input
                type="number"
                value={params.initialAmount}
                onChange={(e) => updateParam('initialAmount', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">网格设置</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                网格数量
              </label>
              <input
                type="number"
                value={params.gridCount}
                onChange={(e) => updateParam('gridCount', Number(e.target.value))}
                min="5"
                max="50"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                价格下限
              </label>
              <input
                type="number"
                value={params.priceRange.min}
                onChange={(e) => updatePriceRange('min', Number(e.target.value))}
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                价格上限
              </label>
              <input
                type="number"
                value={params.priceRange.max}
                onChange={(e) => updatePriceRange('max', Number(e.target.value))}
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">交易设置</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                每格投资额 (元)
              </label>
              <input
                type="number"
                value={params.investmentPerGrid}
                onChange={(e) => updateParam('investmentPerGrid', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                调仓频率
              </label>
              <select
                value={params.rebalanceFrequency}
                onChange={(e) => updateParam('rebalanceFrequency', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="daily">每日</option>
                <option value="weekly">每周</option>
                <option value="monthly">每月</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                止损比例 (%)
              </label>
              <input
                type="number"
                value={params.stopLoss || ''}
                onChange={(e) => updateParam('stopLoss', e.target.value ? Number(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* 执行按钮 */}
        <div className="flex justify-center mb-6">
          <button
            onClick={runGridTradingTest}
            disabled={loading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? '回测中...' : '开始网格交易回测'}
          </button>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* 回测结果 */}
        {result && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-gray-800">回测结果</h3>
            
            {/* 关键指标 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-blue-600 font-medium">总收益率</div>
                <div className="text-2xl font-bold text-blue-900">
                  {result.performance.totalReturn.toFixed(2)}%
                </div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm text-green-600 font-medium">年化收益率</div>
                <div className="text-2xl font-bold text-green-900">
                  {(result.performance.annualizedReturn * 100).toFixed(2)}%
                </div>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-sm text-yellow-600 font-medium">最大回撤</div>
                <div className="text-2xl font-bold text-yellow-900">
                  {result.performance.maxDrawdown.toFixed(2)}%
                </div>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-sm text-purple-600 font-medium">夏普比率</div>
                <div className="text-2xl font-bold text-purple-900">
                  {result.performance.sharpeRatio.toFixed(2)}
                </div>
              </div>
            </div>

            {/* 投资明细 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-4">投资明细 (最近10条)</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        日期
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        投资金额
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        累计投资
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        持有份额
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        当前价值
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        收益率
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {result.timeline.slice(-10).map((entry, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {entry.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ¥{entry.investment.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ¥{entry.totalInvestment.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {entry.shares.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ¥{entry.value.toLocaleString()}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                          entry.return >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {entry.return.toFixed(2)}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
